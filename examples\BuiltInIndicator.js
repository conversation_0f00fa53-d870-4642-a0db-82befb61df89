const TradingView = require('../main');

/**
 * Enhanced Built-in Indicator Example
 * This example demonstrates multiple indicators and different markets
 */

console.log('🚀 Starting TradingView API Enhanced Example...\n');

// Create client without authentication (for free indicators)
const client = new TradingView.Client();

// Create chart session
const chart = new client.Session.Chart();

// Set initial market - Bitcoin/Euro on Binance
console.log('📊 Setting up BINANCE:BTCEUR chart...');
chart.setMarket('BINANCE:BTCEUR', {
  timeframe: '60', // 1 hour timeframe
  range: 1,
});

// Chart event handlers
chart.onError((...err) => {
  console.error('❌ Chart error:', ...err);
});

chart.onSymbolLoaded(() => {
  console.log(`✅ Market "${chart.infos.description}" loaded successfully!`);
  console.log(`💰 Current price: ${chart.periods[0]?.close || 'Loading...'} ${chart.infos.currency_id}\n`);
});

chart.onUpdate(() => {
  if (!chart.periods[0]) return;
  console.log(`📈 [${chart.infos.description}]: ${chart.periods[0].close} ${chart.infos.currency_id}`);
});

// Volume Profile Indicator
const volumeProfile = new TradingView.BuiltInIndicator('VbPFixed@tv-basicstudies-241!');
volumeProfile.setOption('first_bar_time', Date.now() - 10 ** 8);

const VOL = new chart.Study(volumeProfile);
VOL.onUpdate(() => {
  console.log('\n📊 Volume Profile Analysis:');
  VOL.graphic.horizHists
    .filter((h) => h.lastBarTime === 0) // Recent volume data only
    .sort((a, b) => b.priceHigh - a.priceHigh)
    .slice(0, 10) // Show top 10 volume levels
    .forEach((h, index) => {
      const price = Math.round((h.priceHigh + h.priceLow) / 2);
      const volumeBar = '_'.repeat(Math.max(1, (h.rate[0] + h.rate[1]) / 5));
      console.log(`${index + 1}. ${price} € : ${volumeBar}`);
    });

  // Switch to different market after 5 seconds
  setTimeout(() => {
    console.log('\n🔄 Switching to Ethereum...');
    chart.setMarket('BINANCE:ETHEUR', {
      timeframe: '15', // 15 minute timeframe
    });
  }, 5000);

  // End after 15 seconds
  setTimeout(() => {
    console.log('\n✅ Example completed successfully!');
    client.end();
  }, 15000);
});
