/**
 * Portfolio Tracking System for EGX Stock Monitor
 * Track personal investments, P&L, and performance metrics
 */

class PortfolioTracker {
  constructor() {
    this.portfolios = new Map();
    this.transactions = new Map();
    this.currentPrices = new Map();
  }

  // Create new portfolio
  createPortfolio(userId, portfolioName, initialCash = 0) {
    const portfolioId = `${userId}_${Date.now()}`;
    
    this.portfolios.set(portfolioId, {
      id: portfolioId,
      userId,
      name: portfolioName,
      created: new Date(),
      initialCash,
      currentCash: initialCash,
      positions: new Map(),
      totalInvested: 0,
      totalValue: 0,
      totalPnL: 0,
      totalPnLPercent: 0
    });

    return portfolioId;
  }

  // Add transaction (buy/sell)
  addTransaction(portfolioId, symbol, type, quantity, price, fees = 0, date = new Date()) {
    const portfolio = this.portfolios.get(portfolioId);
    if (!portfolio) throw new Error('Portfolio not found');

    const transactionId = `${portfolioId}_${Date.now()}`;
    const totalCost = (quantity * price) + fees;

    const transaction = {
      id: transactionId,
      portfolioId,
      symbol,
      type, // 'buy' or 'sell'
      quantity,
      price,
      fees,
      totalCost,
      date
    };

    // Store transaction
    this.transactions.set(transactionId, transaction);

    // Update portfolio
    this.updatePortfolioPosition(portfolioId, transaction);

    return transactionId;
  }

  updatePortfolioPosition(portfolioId, transaction) {
    const portfolio = this.portfolios.get(portfolioId);
    const { symbol, type, quantity, price, totalCost } = transaction;

    let position = portfolio.positions.get(symbol) || {
      symbol,
      quantity: 0,
      averagePrice: 0,
      totalInvested: 0,
      currentValue: 0,
      pnl: 0,
      pnlPercent: 0,
      transactions: []
    };

    position.transactions.push(transaction);

    if (type === 'buy') {
      // Calculate new average price
      const newTotalInvested = position.totalInvested + totalCost;
      const newQuantity = position.quantity + quantity;
      
      position.averagePrice = newTotalInvested / newQuantity;
      position.quantity = newQuantity;
      position.totalInvested = newTotalInvested;
      
      portfolio.currentCash -= totalCost;
      portfolio.totalInvested += totalCost;

    } else if (type === 'sell') {
      if (position.quantity < quantity) {
        throw new Error('Insufficient shares to sell');
      }

      const soldValue = (quantity * price) - transaction.fees;
      const soldCost = (quantity / position.quantity) * position.totalInvested;
      
      position.quantity -= quantity;
      position.totalInvested -= soldCost;
      
      if (position.quantity > 0) {
        // Recalculate average price for remaining shares
        position.averagePrice = position.totalInvested / position.quantity;
      } else {
        position.averagePrice = 0;
        position.totalInvested = 0;
      }

      portfolio.currentCash += soldValue;
      portfolio.totalInvested -= soldCost;
    }

    portfolio.positions.set(symbol, position);
    this.calculatePortfolioMetrics(portfolioId);
  }

  // Update current prices and recalculate portfolio values
  updatePrice(symbol, currentPrice) {
    this.currentPrices.set(symbol, currentPrice);
    
    // Update all portfolios that have this symbol
    this.portfolios.forEach((portfolio, portfolioId) => {
      if (portfolio.positions.has(symbol)) {
        this.calculatePortfolioMetrics(portfolioId);
      }
    });
  }

  calculatePortfolioMetrics(portfolioId) {
    const portfolio = this.portfolios.get(portfolioId);
    if (!portfolio) return;

    let totalCurrentValue = portfolio.currentCash;
    let totalPnL = 0;

    portfolio.positions.forEach((position, symbol) => {
      const currentPrice = this.currentPrices.get(symbol) || position.averagePrice;
      
      position.currentValue = position.quantity * currentPrice;
      position.pnl = position.currentValue - position.totalInvested;
      position.pnlPercent = position.totalInvested > 0 
        ? (position.pnl / position.totalInvested) * 100 
        : 0;

      totalCurrentValue += position.currentValue;
      totalPnL += position.pnl;
    });

    portfolio.totalValue = totalCurrentValue;
    portfolio.totalPnL = totalPnL;
    portfolio.totalPnLPercent = portfolio.totalInvested > 0 
      ? (totalPnL / portfolio.totalInvested) * 100 
      : 0;
  }

  // Get portfolio summary
  getPortfolioSummary(portfolioId) {
    const portfolio = this.portfolios.get(portfolioId);
    if (!portfolio) return null;

    const positions = Array.from(portfolio.positions.values())
      .filter(pos => pos.quantity > 0)
      .map(pos => ({
        ...pos,
        weight: portfolio.totalValue > 0 ? (pos.currentValue / portfolio.totalValue) * 100 : 0
      }));

    return {
      ...portfolio,
      positions: positions,
      performance: this.getPortfolioPerformance(portfolioId)
    };
  }

  // Get portfolio performance metrics
  getPortfolioPerformance(portfolioId) {
    const portfolio = this.portfolios.get(portfolioId);
    if (!portfolio) return null;

    const transactions = Array.from(this.transactions.values())
      .filter(t => t.portfolioId === portfolioId)
      .sort((a, b) => a.date - b.date);

    const positions = Array.from(portfolio.positions.values());
    
    return {
      totalReturn: portfolio.totalPnL,
      totalReturnPercent: portfolio.totalPnLPercent,
      dayChange: this.calculateDayChange(portfolioId),
      bestPerformer: positions.reduce((best, pos) => 
        !best || pos.pnlPercent > best.pnlPercent ? pos : best, null),
      worstPerformer: positions.reduce((worst, pos) => 
        !worst || pos.pnlPercent < worst.pnlPercent ? pos : worst, null),
      totalTransactions: transactions.length,
      diversification: this.calculateDiversification(positions)
    };
  }

  calculateDayChange(portfolioId) {
    // This would require storing previous day's values
    // For now, return 0 - implement with historical data
    return { amount: 0, percent: 0 };
  }

  calculateDiversification(positions) {
    if (positions.length === 0) return 0;
    
    const totalValue = positions.reduce((sum, pos) => sum + pos.currentValue, 0);
    if (totalValue === 0) return 0;

    // Calculate Herfindahl-Hirschman Index for diversification
    const hhi = positions.reduce((sum, pos) => {
      const weight = pos.currentValue / totalValue;
      return sum + (weight * weight);
    }, 0);

    // Convert to diversification score (0-100, higher is more diversified)
    return Math.max(0, (1 - hhi) * 100);
  }

  // Get user's portfolios
  getUserPortfolios(userId) {
    return Array.from(this.portfolios.values())
      .filter(portfolio => portfolio.userId === userId)
      .map(portfolio => this.getPortfolioSummary(portfolio.id));
  }

  // Get transaction history
  getTransactionHistory(portfolioId, limit = 50) {
    return Array.from(this.transactions.values())
      .filter(t => t.portfolioId === portfolioId)
      .sort((a, b) => b.date - a.date)
      .slice(0, limit);
  }

  // Generate portfolio report
  generatePortfolioReport(portfolioId) {
    const summary = this.getPortfolioSummary(portfolioId);
    if (!summary) return null;

    const report = {
      portfolioName: summary.name,
      generatedAt: new Date(),
      overview: {
        totalValue: summary.totalValue,
        totalInvested: summary.totalInvested,
        totalPnL: summary.totalPnL,
        totalPnLPercent: summary.totalPnLPercent,
        cashPosition: summary.currentCash
      },
      positions: summary.positions.map(pos => ({
        symbol: pos.symbol,
        quantity: pos.quantity,
        averagePrice: pos.averagePrice,
        currentValue: pos.currentValue,
        pnl: pos.pnl,
        pnlPercent: pos.pnlPercent,
        weight: pos.weight
      })),
      performance: summary.performance,
      recommendations: this.generateRecommendations(summary)
    };

    return report;
  }

  generateRecommendations(portfolio) {
    const recommendations = [];
    
    // Check for over-concentration
    portfolio.positions.forEach(pos => {
      if (pos.weight > 20) {
        recommendations.push({
          type: 'warning',
          message: `${pos.symbol} represents ${pos.weight.toFixed(1)}% of your portfolio. Consider reducing concentration.`
        });
      }
    });

    // Check for poor performers
    portfolio.positions.forEach(pos => {
      if (pos.pnlPercent < -20) {
        recommendations.push({
          type: 'alert',
          message: `${pos.symbol} is down ${Math.abs(pos.pnlPercent).toFixed(1)}%. Review your investment thesis.`
        });
      }
    });

    // Diversification recommendation
    if (portfolio.performance.diversification < 50) {
      recommendations.push({
        type: 'suggestion',
        message: 'Consider diversifying across more sectors to reduce risk.'
      });
    }

    return recommendations;
  }
}

module.exports = PortfolioTracker;
