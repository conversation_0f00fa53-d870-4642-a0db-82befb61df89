const TradingView = require('../main');

/**
 * Custom Indicator Example
 * This example shows how to use different built-in indicators
 */

console.log('🔧 Custom Indicator Example\n');

const client = new TradingView.Client();
const chart = new client.Session.Chart();

// Set up the chart
chart.setMarket('BINANCE:BTCUSDT', {
  timeframe: '60', // 1 hour
  range: 50, // Get 50 periods of data
});

chart.onSymbolLoaded(() => {
  console.log(`✅ Chart loaded: ${chart.infos.description}`);
  console.log(`💰 Current price: $${chart.periods[0]?.close || 'Loading...'}\n`);
});

// Add multiple indicators
const indicators = [];

// 1. RSI Indicator
const rsiIndicator = new TradingView.BuiltInIndicator('RSI@tv-basicstudies-241');
rsiIndicator.setOption('length', 14); // 14-period RSI
const RSI = new chart.Study(rsiIndicator);

// 2. Moving Average
const maIndicator = new TradingView.BuiltInIndicator('MASimple@tv-basicstudies-241');
maIndicator.setOption('length', 20); // 20-period MA
const MA = new chart.Study(maIndicator);

// 3. Bollinger Bands
const bbIndicator = new TradingView.BuiltInIndicator('BollingerBands@tv-basicstudies-241');
bbIndicator.setOption('length', 20);
bbIndicator.setOption('mult', 2);
const BB = new chart.Study(bbIndicator);

// 4. Volume
const volumeIndicator = new TradingView.BuiltInIndicator('Volume@tv-basicstudies-241');
const VOL = new chart.Study(volumeIndicator);

let updateCount = 0;

// Handle indicator updates
RSI.onUpdate(() => {
  updateCount++;
  if (updateCount % 5 !== 0) return; // Update every 5th time to reduce spam
  
  console.log('\n📊 Technical Indicators Update:');
  console.log('================================');
  
  // Current price
  const currentPrice = chart.periods[0]?.close;
  if (currentPrice) {
    console.log(`💰 Price: $${currentPrice.toFixed(2)}`);
  }
  
  // RSI Analysis
  const rsiValue = RSI.periods[0]?.RSI;
  if (rsiValue) {
    let rsiSignal = '';
    if (rsiValue > 70) rsiSignal = '🔴 OVERBOUGHT';
    else if (rsiValue < 30) rsiSignal = '🟢 OVERSOLD';
    else rsiSignal = '🟡 NEUTRAL';
    
    console.log(`📈 RSI (14): ${rsiValue.toFixed(2)} ${rsiSignal}`);
  }
  
  // Moving Average Analysis
  const maValue = MA.periods[0]?.MA;
  if (maValue && currentPrice) {
    const trend = currentPrice > maValue ? '📈 BULLISH' : '📉 BEARISH';
    console.log(`📊 MA (20): $${maValue.toFixed(2)} ${trend}`);
  }
  
  // Bollinger Bands Analysis
  const bbUpper = BB.periods[0]?.upper;
  const bbLower = BB.periods[0]?.lower;
  const bbMiddle = BB.periods[0]?.median;
  
  if (bbUpper && bbLower && bbMiddle && currentPrice) {
    let bbSignal = '';
    if (currentPrice > bbUpper) bbSignal = '🔴 ABOVE UPPER BAND';
    else if (currentPrice < bbLower) bbSignal = '🟢 BELOW LOWER BAND';
    else bbSignal = '🟡 WITHIN BANDS';
    
    console.log(`📏 Bollinger Bands:`);
    console.log(`   Upper: $${bbUpper.toFixed(2)}`);
    console.log(`   Middle: $${bbMiddle.toFixed(2)}`);
    console.log(`   Lower: $${bbLower.toFixed(2)}`);
    console.log(`   Signal: ${bbSignal}`);
  }
  
  // Volume Analysis
  const volume = VOL.periods[0]?.Volume;
  if (volume) {
    console.log(`📊 Volume: ${volume.toLocaleString()}`);
  }
  
  console.log('================================\n');
});

// Error handling
chart.onError((...err) => {
  console.error('❌ Chart error:', ...err);
});

RSI.onError((...err) => {
  console.error('❌ RSI error:', ...err);
});

// Cleanup after 60 seconds
setTimeout(() => {
  console.log('✅ Example completed!');
  client.end();
}, 60000);

console.log('⏳ Loading indicators...\n');
