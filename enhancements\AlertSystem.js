const nodemailer = require('nodemailer');
const twilio = require('twilio');

/**
 * Advanced Alert System for EGX Stock Monitor
 * Supports Email, SMS, Discord, and Telegram notifications
 */

class AlertSystem {
  constructor(config) {
    this.config = config;
    this.alerts = new Map();
    this.setupEmailTransporter();
    this.setupSMSClient();
  }

  setupEmailTransporter() {
    if (this.config.email) {
      this.emailTransporter = nodemailer.createTransporter({
        service: 'gmail',
        auth: {
          user: this.config.email.user,
          pass: this.config.email.password
        }
      });
    }
  }

  setupSMSClient() {
    if (this.config.sms) {
      this.smsClient = twilio(this.config.sms.accountSid, this.config.sms.authToken);
    }
  }

  // Create price alert
  createPriceAlert(symbol, condition, targetPrice, userId, notificationMethods) {
    const alertId = `${symbol}_${Date.now()}`;
    
    this.alerts.set(alertId, {
      id: alertId,
      symbol,
      condition, // 'above', 'below', 'change_percent'
      targetPrice,
      userId,
      notificationMethods, // ['email', 'sms', 'discord', 'telegram']
      created: new Date(),
      triggered: false
    });

    return alertId;
  }

  // Check alerts against current price
  checkAlerts(stockData) {
    this.alerts.forEach((alert, alertId) => {
      if (alert.triggered || alert.symbol !== stockData.symbol) return;

      let shouldTrigger = false;
      let message = '';

      switch (alert.condition) {
        case 'above':
          if (stockData.price >= alert.targetPrice) {
            shouldTrigger = true;
            message = `🚀 ${stockData.name} (${stockData.symbol}) is now ${stockData.price.toFixed(2)} EGP (above your target of ${alert.targetPrice} EGP)`;
          }
          break;

        case 'below':
          if (stockData.price <= alert.targetPrice) {
            shouldTrigger = true;
            message = `📉 ${stockData.name} (${stockData.symbol}) is now ${stockData.price.toFixed(2)} EGP (below your target of ${alert.targetPrice} EGP)`;
          }
          break;

        case 'change_percent':
          if (Math.abs(stockData.changePercent) >= alert.targetPrice) {
            shouldTrigger = true;
            message = `⚡ ${stockData.name} (${stockData.symbol}) moved ${stockData.changePercent.toFixed(2)}% today!`;
          }
          break;
      }

      if (shouldTrigger) {
        this.triggerAlert(alert, message, stockData);
      }
    });
  }

  async triggerAlert(alert, message, stockData) {
    alert.triggered = true;
    alert.triggeredAt = new Date();

    console.log(`🔔 Alert triggered: ${message}`);

    // Send notifications based on user preferences
    for (const method of alert.notificationMethods) {
      try {
        switch (method) {
          case 'email':
            await this.sendEmailAlert(alert, message, stockData);
            break;
          case 'sms':
            await this.sendSMSAlert(alert, message);
            break;
          case 'discord':
            await this.sendDiscordAlert(alert, message, stockData);
            break;
          case 'telegram':
            await this.sendTelegramAlert(alert, message, stockData);
            break;
        }
      } catch (error) {
        console.error(`Failed to send ${method} alert:`, error);
      }
    }
  }

  async sendEmailAlert(alert, message, stockData) {
    if (!this.emailTransporter) return;

    const htmlContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2c3e50;">🇪🇬 EGX Stock Alert</h2>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
          <h3 style="color: #e74c3c;">${message}</h3>
          <table style="width: 100%; border-collapse: collapse; margin-top: 20px;">
            <tr><td><strong>Stock:</strong></td><td>${stockData.name}</td></tr>
            <tr><td><strong>Symbol:</strong></td><td>${stockData.symbol}</td></tr>
            <tr><td><strong>Current Price:</strong></td><td>${stockData.price.toFixed(2)} EGP</td></tr>
            <tr><td><strong>Daily Change:</strong></td><td>${stockData.change.toFixed(2)} EGP (${stockData.changePercent.toFixed(2)}%)</td></tr>
            <tr><td><strong>Volume:</strong></td><td>${stockData.volume.toLocaleString()}</td></tr>
          </table>
        </div>
        <p style="color: #7f8c8d; font-size: 12px;">
          This alert was triggered at ${new Date().toLocaleString()}<br>
          EGX Stock Monitor - Real-time Egyptian Exchange
        </p>
      </div>
    `;

    await this.emailTransporter.sendMail({
      from: this.config.email.user,
      to: alert.userId, // Assuming userId is email
      subject: `🚨 EGX Alert: ${stockData.name}`,
      html: htmlContent
    });
  }

  async sendSMSAlert(alert, message) {
    if (!this.smsClient) return;

    await this.smsClient.messages.create({
      body: `EGX Alert: ${message}`,
      from: this.config.sms.fromNumber,
      to: alert.userId // Assuming userId is phone number for SMS
    });
  }

  async sendDiscordAlert(alert, message, stockData) {
    const webhook = this.config.discord?.webhook;
    if (!webhook) return;

    const embed = {
      title: "🇪🇬 EGX Stock Alert",
      description: message,
      color: stockData.change >= 0 ? 0x27ae60 : 0xe74c3c,
      fields: [
        { name: "Price", value: `${stockData.price.toFixed(2)} EGP`, inline: true },
        { name: "Change", value: `${stockData.change.toFixed(2)} EGP (${stockData.changePercent.toFixed(2)}%)`, inline: true },
        { name: "Volume", value: stockData.volume.toLocaleString(), inline: true }
      ],
      timestamp: new Date().toISOString(),
      footer: { text: "EGX Stock Monitor" }
    };

    await fetch(webhook, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ embeds: [embed] })
    });
  }

  async sendTelegramAlert(alert, message, stockData) {
    const botToken = this.config.telegram?.botToken;
    const chatId = this.config.telegram?.chatId;
    if (!botToken || !chatId) return;

    const telegramMessage = `
🇪🇬 *EGX Stock Alert*

${message}

📊 *Details:*
• Price: ${stockData.price.toFixed(2)} EGP
• Change: ${stockData.change.toFixed(2)} EGP (${stockData.changePercent.toFixed(2)}%)
• Volume: ${stockData.volume.toLocaleString()}

_Triggered at ${new Date().toLocaleString()}_
    `;

    await fetch(`https://api.telegram.org/bot${botToken}/sendMessage`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        chat_id: chatId,
        text: telegramMessage,
        parse_mode: 'Markdown'
      })
    });
  }

  // Get all alerts for a user
  getUserAlerts(userId) {
    return Array.from(this.alerts.values()).filter(alert => alert.userId === userId);
  }

  // Remove alert
  removeAlert(alertId) {
    return this.alerts.delete(alertId);
  }

  // Get alert statistics
  getAlertStats() {
    const alerts = Array.from(this.alerts.values());
    return {
      total: alerts.length,
      triggered: alerts.filter(a => a.triggered).length,
      active: alerts.filter(a => !a.triggered).length,
      bySymbol: alerts.reduce((acc, alert) => {
        acc[alert.symbol] = (acc[alert.symbol] || 0) + 1;
        return acc;
      }, {})
    };
  }
}

module.exports = AlertSystem;
