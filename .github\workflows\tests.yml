name: Tests

on:
  push:
  pull_request:
    branches:
      - main
  workflow_dispatch:
  schedule:
  - cron: '0 0 * * *'

jobs:
  Tests:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [14.x, 18.x, 19.x]

    steps:
    - uses: actions/checkout@v2
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
    - name: Install dependencies
      run: npm ci
    - name: Run tests
      run: npm test
      env:
        SESSION: ${{ secrets.TW_SESSION }}
        SIGNATURE: ${{ secrets.TW_SIGNATURE }}
